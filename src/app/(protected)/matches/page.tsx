'use client'

import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import Image from 'next/image'

interface MatchProfile {
  id: string
  profile: {
    fullName: string
    dateOfBirth: string
    town: { name: string }
    tribe?: string
    profilePictures: { url: string; isPrimary: boolean }[]
  }
}

export default function MatchesPage() {
  const [matches, setMatches] = useState<MatchProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchMatches = async () => {
      setLoading(true)
      setError('')
      try {
        const token = localStorage.getItem('kenyamatch_token')
        const res = await fetch('/api/matches', {
          headers: { Authorization: `Bearer ${token}` }
        })
        const data = await res.json()
        if (data.success) {
          setMatches(data.data)
        } else {
          setError(data.error || 'Failed to load matches')
        }
      } catch (err) {
        setError('Failed to load matches')
      } finally {
        setLoading(false)
      }
    }
    fetchMatches()
  }, [])

  const handleMatch = async (targetUserId: string) => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/matches', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ targetUserId })
      })
      const data = await res.json()
      if (data.success) {
        toast.success('Match request sent!')
      } else {
        toast.error(data.error || 'Could not send match request')
      }
    } catch (err) {
      toast.error('Could not send match request')
    }
  }

  if (loading) return <div className="text-center py-12">Loading matches...</div>
  if (error) return <div className="text-center text-red-500 py-12">{error}</div>

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6 text-gray-800">Potential Matches</h1>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {matches.map((user) => (
          <div key={user.id} className="bg-white rounded-xl shadow p-4 flex flex-col items-center">
            <div className="w-24 h-24 rounded-full overflow-hidden mb-3 bg-gray-200 flex items-center justify-center">
              {user.profile.profilePictures[0]?.url ? (
                <Image
                  src={user.profile.profilePictures[0].url}
                  alt={user.profile.fullName}
                  width={96}
                  height={96}
                  className="object-cover w-full h-full"
                />
              ) : (
                <span className="text-3xl text-gray-500 font-bold">
                  {user.profile.fullName[0]}
                </span>
              )}
            </div>
            <div className="text-lg font-semibold text-gray-800">{user.profile.fullName}</div>
            <div className="text-gray-700 text-sm">
              {user.profile.town.name}
              {user.profile.tribe && ` • ${user.profile.tribe}`}
            </div>
            <button
              className="mt-4 bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded-full font-medium"
              onClick={() => handleMatch(user.id)}
            >
              Match
            </button>
          </div>
        ))}
      </div>
    </div>
  )
} 