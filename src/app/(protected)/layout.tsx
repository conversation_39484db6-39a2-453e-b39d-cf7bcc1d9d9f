'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import { Heart, Users, MessageCircle, User, Settings, LogOut } from 'lucide-react'

const navLinks = [
  { href: '/matches', label: 'Matches', icon: <Users className="h-5 w-5" /> },
  { href: '/messages', label: 'Messages', icon: <MessageCircle className="h-5 w-5" /> },
  { href: '/profile', label: 'Profile', icon: <User className="h-5 w-5" /> },
  { href: '/settings', label: 'Settings', icon: <Settings className="h-5 w-5" /> },
]

export default function ProtectedLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const token = typeof window !== 'undefined' ? localStorage.getItem('kenyamatch_token') : null
    if (!token) {
      router.replace('/login')
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('kenyamatch_token')
    router.replace('/login')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
      <nav className="bg-white/90 border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-5xl mx-auto px-4 flex items-center justify-between h-16">
          <div className="flex items-center gap-2">
            <Heart className="h-7 w-7 text-pink-500" />
            <span className="font-bold text-lg text-gray-900">KenyaMatch</span>
          </div>
          <div className="flex items-center gap-4">
            {navLinks.map(link => (
              <Link
                key={link.href}
                href={link.href}
                className={`flex items-center gap-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${pathname.startsWith(link.href) ? 'bg-pink-100 text-pink-600' : 'text-gray-700 hover:bg-gray-100'}`}
              >
                {link.icon}
                {link.label}
              </Link>
            ))}
            <button
              onClick={handleLogout}
              className="flex items-center gap-1 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100"
            >
              <LogOut className="h-5 w-5" /> Logout
            </button>
          </div>
        </div>
      </nav>
      <main className="max-w-5xl mx-auto py-8 px-4">
        {children}
      </main>
    </div>
  )
} 