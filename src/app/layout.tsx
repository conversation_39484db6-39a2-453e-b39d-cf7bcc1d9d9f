import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "KenyaMatch - Find Your Perfect Match in Kenya",
  description: "Kenya's premier dating platform. Connect with genuine people, build meaningful relationships, and find your perfect match in Kenya.",
  keywords: "dating, kenya, match, relationship, love, kenyan dating",
  authors: [{ name: "KenyaMatch Team" }],
  openGraph: {
    title: "KenyaMatch - Find Your Perfect Match in Kenya",
    description: "Connect with genuine people and build meaningful relationships in Kenya.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "KenyaMatch - Find Your Perfect Match in Kenya",
    description: "Connect with genuine people and build meaningful relationships in Kenya.",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: "#363636",
              color: "#fff",
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: "#4ade80",
                secondary: "#fff",
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: "#ef4444",
                secondary: "#fff",
              },
            },
          }}
        />
      </body>
    </html>
  );
}
