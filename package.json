{"name": "ken<PERSON>tch", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.22.0", "@stripe/stripe-js": "^2.4.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "clsx": "^2.0.0", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.294.0", "multer": "^1.4.5-lts.1", "next": "15.3.5", "next-auth": "^4.24.5", "prisma": "^5.22.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "sharp": "^0.33.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "stripe": "^14.17.0", "tailwind-merge": "^2.2.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tsx": "^4.7.0", "typescript": "^5"}}