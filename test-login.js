// Simple test script to verify login functionality
const fetch = require('node-fetch');

async function testLogin() {
  console.log('🧪 Testing login functionality...\n');
  
  // Test regular user login
  console.log('1. Testing regular user login...');
  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'demo12345'
      })
    });
    
    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ Regular user login successful');
      console.log('User role:', data.data.user.role);
      console.log('Has profile:', !!data.data.user.profile);
      console.log('Profile complete:', !!(data.data.user.profile?.fullName && data.data.user.profile?.dateOfBirth));
    } else {
      console.log('❌ Regular user login failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Regular user login error:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test admin user login
  console.log('2. Testing admin user login...');
  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'changeme'
      })
    });
    
    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ Admin user login successful');
      console.log('User role:', data.data.user.role);
      console.log('Is admin:', data.data.user.isAdmin);
    } else {
      console.log('❌ Admin user login failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Admin user login error:', error.message);
  }
}

testLogin().catch(console.error);
