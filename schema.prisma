// Prisma Schema for KenyaMatch Dating App

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums for better type safety and consistency
enum Gender {
  MALE
  FEMALE
  OTHER
}

enum PaymentMethod {
  MPESA
  STRIPE
  OTHER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
}

enum ChildrenPreference {
  YES
  NO
  DOESNT_MATTER
}

model User {
  id                    String     @id @default(uuid())
  email                 String     @unique
  phone                 String?    @unique
  passwordHash          String
  name                  String?    // Added missing name field
  role                  String     @default("USER") // Added missing role field
  isPaid               <PERSON>olean    @default(false)
  subscriptionExpiresAt DateTime? 
  isAd<PERSON>    @default(false)
  isActive             Boolean    @default(true)
  lastLoginAt          DateTime?
  createdAt            DateTime   @default(now())
  updatedAt            DateTime   @updatedAt

  profile              Profile?
  children             Children?
  preferences          Preferences?
  discoveryPreferences DiscoveryPreferences?
  matchesInitiated     Match[]    @relation("User1Matches")
  matchesReceived      Match[]    @relation("User2Matches")
  messagesSent         Message[]  @relation("SentMessages")
  messagesReceived     Message[]  @relation("ReceivedMessages")
  payments             Payment[]
  blocked              BlockedUser[] @relation("Blocker")
  blockedBy            BlockedUser[] @relation("Blocked")
  reportsMade          Report[]   @relation("Reporter")
  reportsReceived      Report[]   @relation("Reported")
  notifications        Notification[]
  compatibilities1     MatchCompatibility[] @relation("User1Compatibility")
  compatibilities2     MatchCompatibility[] @relation("User2Compatibility")
  activities           UserActivity[]
  targetActivities     UserActivity[] @relation("TargetUserActivity")
  searchHistory        SearchHistory[]

  @@index([email])
  @@index([phone])
  @@index([isPaid])
  @@index([role])
}

model Profile {
  id               String   @id @default(uuid())
  userId           String   @unique
  fullName         String
  gender           Gender
  genderPreference Gender
  dateOfBirth      DateTime
  townId           String
  bio              String?  @db.Text
  tribe            String?
  religion         String?
  occupation       String?
  education        String?  // Added education field
  relationshipGoal String?  // marriage, serious, casual, friendship
  profilePictures  ProfilePicture[]
  verified         Boolean  @default(false)
  safetyScore      Float    @default(100.0) // 0-100 safety rating
  lastActive       DateTime @default(now())
  isOnline         Boolean  @default(false) // Real-time online status

  user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  town             Town     @relation(fields: [townId], references: [id])
  verifications    UserVerification[]

  @@index([gender])
  @@index([townId])
  @@index([dateOfBirth])
  @@index([verified])
  @@index([safetyScore])
  @@index([isOnline])
}

model ProfilePicture {
  id          String   @id @default(uuid())
  profileId   String
  url         String
  isPrimary   Boolean  @default(false)
  isApproved  Boolean  @default(false)
  moderationReason String?
  fileSize    Int?
  dimensions  Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@index([profileId])
  @@index([isApproved])
}

model Town {
  id        String    @id @default(uuid())
  name      String    @unique
  county    String
  latitude  Float
  longitude Float
  profiles  Profile[]

  @@index([name])
  @@index([county])
  @@index([latitude, longitude])
}

model Children {
  id       String   @id @default(uuid())
  userId   String   @unique
  count    Int
  genders  Gender[]

  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Preferences {
  id                 String            @id @default(uuid())
  userId             String            @unique
  preferredAgeMin    Int
  preferredAgeMax    Int
  hasChildren        ChildrenPreference
  maxDistance        Int               @default(50) // in kilometers
  tribePreference    String?
  religionPreference String?

  user               User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([preferredAgeMin, preferredAgeMax])
}

model Match {
  id         String    @id @default(uuid())
  user1Id    String
  user2Id    String
  status     String    @default("pending") // pending, accepted, rejected
  matchedAt  DateTime  @default(now())
  messages   Message[]

  user1      User      @relation("User1Matches", fields: [user1Id], references: [id], onDelete: Cascade)
  user2      User      @relation("User2Matches", fields: [user2Id], references: [id], onDelete: Cascade)

  @@unique([user1Id, user2Id])
  @@index([user1Id])
  @@index([user2Id])
  @@index([status])
}

model Message {
  id          String   @id @default(uuid())
  matchId     String
  senderId    String
  receiverId  String
  content     String   @db.Text
  isRead      Boolean  @default(false)
  createdAt   DateTime @default(now())

  match       Match    @relation(fields: [matchId], references: [id], onDelete: Cascade)
  sender      User     @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  receiver    User     @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)

  @@index([matchId])
  @@index([senderId])
  @@index([receiverId])
  @@index([createdAt])
}

model SubscriptionPlan {
  id          String   @id @default(uuid())
  name        String   // Basic, Premium, VIP
  price       Float
  duration    Int      // Days
  features    Json     // Array of feature strings
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  payments    Payment[]

  @@index([isActive])
}

model Payment {
  id            String        @id @default(uuid())
  userId        String
  planId        String?       // Reference to subscription plan
  amount        Float
  method        PaymentMethod
  status        PaymentStatus @default(PENDING)
  transactionId String?       @unique // For M-Pesa/Stripe reference
  metadata      Json?         // For additional payment details
  refundedAt    DateTime?     // When refund was processed
  refundReason  String?       // Reason for refund
  paidAt        DateTime      @default(now())

  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan          SubscriptionPlan? @relation(fields: [planId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([paidAt])
  @@index([planId])
}

model BlockedUser {
  id          String   @id @default(uuid())
  blockerId   String
  blockedId   String
  reason      String?
  createdAt   DateTime @default(now())

  blocker     User     @relation("Blocker", fields: [blockerId], references: [id], onDelete: Cascade)
  blocked     User     @relation("Blocked", fields: [blockedId], references: [id], onDelete: Cascade)

  @@unique([blockerId, blockedId])
  @@index([blockerId])
  @@index([blockedId])
}

model Report {
  id              String   @id @default(uuid())
  reporterId      String
  reportedId      String   // Fixed field name to match schema
  reason          String   @db.Text
  description     String?  @db.Text // Added missing description field
  status          String   @default("pending") // pending, reviewed, resolved
  adminNotes      String?  @db.Text
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  reporter        User     @relation("Reporter", fields: [reporterId], references: [id], onDelete: Cascade)
  reported        User     @relation("Reported", fields: [reportedId], references: [id], onDelete: Cascade)

  @@index([reporterId])
  @@index([reportedId])
  @@index([status])
}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  type      String   // match, message, payment, system
  title     String
  message   String   @db.Text
  isRead    Boolean  @default(false)
  metadata  Json?    // For additional notification data
  createdAt DateTime @default(now())

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
  @@index([createdAt])
}

model MatchCompatibility {
  id              String   @id @default(uuid())
  user1Id         String
  user2Id         String
  overallScore    Float    // 0-100 compatibility score
  ageCompatibility Float   // Age preference match score
  locationCompatibility Float // Distance-based score
  interestCompatibility Float // Interest overlap score
  valuesCompatibility Float // Values/religion/tribe match score
  calculatedAt    DateTime @default(now())

  user1           User     @relation("User1Compatibility", fields: [user1Id], references: [id], onDelete: Cascade)
  user2           User     @relation("User2Compatibility", fields: [user2Id], references: [id], onDelete: Cascade)

  @@unique([user1Id, user2Id])
  @@index([user1Id])
  @@index([user2Id])
  @@index([overallScore])
}

model UserActivity {
  id          String   @id @default(uuid())
  userId      String
  activityType String  // profile_view, like, message, match, login
  targetUserId String? // For activities involving another user
  metadata    Json?    // Additional activity data
  createdAt   DateTime @default(now())

  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  targetUser  User?    @relation("TargetUserActivity", fields: [targetUserId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([activityType])
  @@index([createdAt])
  @@index([targetUserId])
}

model UserVerification {
  id          String   @id @default(uuid())
  profileId   String
  type        String   // phone, email, photo, government_id
  status      String   @default("pending") // pending, approved, rejected
  documentUrl String?  // For ID verification
  verifiedAt  DateTime?
  rejectedAt  DateTime?
  rejectionReason String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@index([profileId])
  @@index([type])
  @@index([status])
}

model SearchHistory {
  id          String   @id @default(uuid())
  userId      String
  searchQuery String   // The search term used
  filters     Json     // Applied filters (age, location, etc.)
  resultsCount Int     // Number of results returned
  createdAt   DateTime @default(now())

  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
}

model DiscoveryPreferences {
  id              String   @id @default(uuid())
  userId          String   @unique
  showMeTo        String   @default("everyone") // everyone, matches_only, premium_only
  allowMessagesFrom String @default("matches") // everyone, matches, premium
  profileVisibility String @default("public") // public, matches_only, hidden
  locationSharing Boolean  @default(true)
  onlineStatus    Boolean  @default(true)
  lastSeen        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}